package common

import (
	"fmt"
	"github.com/go-pay/util"
	"math/rand"
	"strconv"
	"time"
	"unicode/utf8"

	"github.com/golang-jwt/jwt/v4"
)

func InSlice(str string, s []string) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func InInterfaceSlice(str interface{}, s []interface{}) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

func InInt64Slice(str int64, s []int64) bool {
	for _, v := range s {
		if str == v {
			return true
		}
	}
	return false
}

// ShuffleInt64Slice 随机打乱slice
func ShuffleInt64Slice(slice []int64) []int64 {
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(slice), func(i, j int) {
		slice[i], slice[j] = slice[j], slice[i]
	})
	return slice
}

// TimeToString 年月日时分秒格式返回
func TimeToString(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// UnixToTime Unix时间戳转换为time.Time
func UnixToTime(unix int64) time.Time {
	if unix == 0 {
		return time.Time{}
	}
	return time.Unix(unix, 0)
}

// DeleteSlice 删除slice里面的某个值
func DeleteSlice(slice []interface{}, elem interface{}) []interface{} {
	tgt := slice[:0]
	for _, v := range slice {
		if v != elem {
			tgt = append(tgt, v)
		}
	}
	return tgt
}

// GetJwtToken 获取 jwt token
// @secretKey: JWT 加解密密钥
// @iat: 时间戳
// @seconds: 过期时间，单位秒
// @payload: 数据载体
func GetJwtToken(secretKey string, iat, seconds int64, payload string) (string, error) {
	claims := make(jwt.MapClaims)
	claims["exp"] = iat + seconds
	claims["iat"] = iat
	claims["payload"] = payload
	token := jwt.New(jwt.SigningMethodHS256)
	token.Claims = claims
	return token.SignedString([]byte(secretKey))
}

// 计算相隔天数
func DaysBetween(start, end int64) int64 {
	startTime := time.Unix(start, 0).Truncate(24 * time.Hour)
	endTime := time.Unix(end, 0).Truncate(24 * time.Hour)

	// 计算相隔自然天数
	days := int64(endTime.Sub(startTime).Hours() / 24)

	return days
}

// SliceReplace 替换切片里面的值
func SliceReplace(slice []string, old string, new string) []string {
	for i, v := range slice {
		if v == old {
			slice[i] = new
			break // 如果只替换第一个匹配项，可以直接退出循环
		}
	}
	return slice
}

// 精度减法计算
func Bcsub(num1, num2 float64, length int64) float64 {
	value := num1 - num2
	str := fmt.Sprintf("%."+fmt.Sprintf("%d", length)+"f", value)
	f, _ := strconv.ParseFloat(str, 64)

	return f
}

// 获取今日0点时间戳
func TodayTimeStamp() int64 {
	// 获取当前时间
	now := time.Now()

	// 获取当天的日期
	year, month, day := now.Date()

	// 构建今天零点的时间
	zeroTime := time.Date(year, month, day, 0, 0, 0, 0, time.Local)

	// 获取零点时间的时间戳
	zeroTimestamp := zeroTime.Unix()
	return zeroTimestamp
}

// 生成订单号
func GenerateOrderNo(prefix string) string {
	timestamp := time.Now().Format("060102")                              // 获取日期部分，类似于 '210226'
	milliseconds := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)[10:] // 获取时间戳的后5位，类似于 '54321'
	microseconds := fmt.Sprintf("%06d", time.Now().UnixNano()%1e6)        // 获取微秒的6位，类似于 '987654'
	randomNumber := fmt.Sprintf("%02d", rand.Intn(100))                   // 生成2位随机数，类似于 '42'

	// 截取微秒的前5位
	if len(microseconds) > 5 {
		microseconds = microseconds[:5]
	}

	OrderNo := prefix + timestamp + milliseconds + microseconds + randomNumber

	return OrderNo
}

// TruncateString 截取文字前maxLength,并添加...
func TruncateString(input string, maxLength int) string {
	// 确保不截取半个字符
	if len(input) <= maxLength {
		return input
	}

	// 使用utf8.RuneCountInString获取字符数
	charCount := utf8.RuneCountInString(input)
	if charCount <= maxLength {
		return input
	}

	// 截取前maxLength个字符
	runes := []rune(input)
	truncated := string(runes[:maxLength])

	// 添加省略号
	return truncated + "..."
}

func Uuid() string {
	return fmt.Sprintf("%d%v", time.Now().Unix(), util.RandomString(6))
}
