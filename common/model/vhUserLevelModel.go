package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ UserLevelModel = (*customUserLevelModel)(nil)

type (
	// UserLevelModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUserLevelModel.
	UserLevelModel interface {
		userLevelModel
	}

	customUserLevelModel struct {
		*defaultUserLevelModel
	}
)

// NewUserLevelModel returns a model for the database table.
func NewUserLevelModel(conn sqlx.SqlConn) UserLevelModel {
	return &customUserLevelModel{
		defaultUserLevelModel: newUserLevelModel(conn),
	}
}
