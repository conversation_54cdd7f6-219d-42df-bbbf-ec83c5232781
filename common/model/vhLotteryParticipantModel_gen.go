// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhLotteryParticipantFieldNames          = builder.RawFieldNames(&VhLotteryParticipant{})
	vhLotteryParticipantRows                = strings.Join(vhLotteryParticipantFieldNames, ",")
	vhLotteryParticipantRowsExpectAutoSet   = strings.Join(stringx.Remove(vhLotteryParticipantFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhLotteryParticipantRowsWithPlaceHolder = strings.Join(stringx.Remove(vhLotteryParticipantFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhLotteryParticipantModel interface {
		Insert(ctx context.Context, data *VhLotteryParticipant) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhLotteryParticipant) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhLotteryParticipant, error)
		FindOneByActivityIdUid(ctx context.Context, activityId uint64, uid uint64) (*VhLotteryParticipant, error)
		Update(ctx context.Context, data *VhLotteryParticipant) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhLotteryParticipantModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhLotteryParticipant struct {
		Id         uint64    `db:"id"`
		ActivityId uint64    `db:"activity_id"`  // 关联的抽奖活动ID
		Uid        uint64    `db:"uid"`          // 参与用户ID
		AddressId  uint64    `db:"address_id"`   // 用户地址ID（用于开奖后自动生成订单）
		JoinTime   time.Time `db:"join_time"`    // 参与时间
		IsWinner   uint64    `db:"is_winner"`    // 是否中奖（0未中奖，1已中奖未生成订单，2已中奖已生成订单）
		SubOrderNo string    `db:"sub_order_no"` // 中奖后生成的订单号
	}
)

func newVhLotteryParticipantModel(conn sqlx.SqlConn) *defaultVhLotteryParticipantModel {
	return &defaultVhLotteryParticipantModel{
		conn:  conn,
		table: "`vh_lottery_participant`",
	}
}

func (m *defaultVhLotteryParticipantModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhLotteryParticipantModel) FindOne(ctx context.Context, id uint64) (*VhLotteryParticipant, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhLotteryParticipantRows, m.table)
	var resp VhLotteryParticipant
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryParticipantModel) FindOneByActivityIdUid(ctx context.Context, activityId uint64, uid uint64) (*VhLotteryParticipant, error) {
	var resp VhLotteryParticipant
	query := fmt.Sprintf("select %s from %s where `activity_id` = ? and `uid` = ? limit 1", vhLotteryParticipantRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, activityId, uid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryParticipantModel) Insert(ctx context.Context, data *VhLotteryParticipant) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, vhLotteryParticipantRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.ActivityId, data.Uid, data.AddressId, data.JoinTime, data.IsWinner, data.SubOrderNo)
	return ret, err
}

func (m *defaultVhLotteryParticipantModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhLotteryParticipant) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?)", m.table, vhLotteryParticipantRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.ActivityId, data.Uid, data.AddressId, data.JoinTime, data.IsWinner, data.SubOrderNo)
	return ret, err
}

func (m *defaultVhLotteryParticipantModel) Update(ctx context.Context, newData *VhLotteryParticipant) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhLotteryParticipantRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, newData.ActivityId, newData.Uid, newData.AddressId, newData.JoinTime, newData.IsWinner, newData.SubOrderNo, newData.Id)
	return err
}

func (m *defaultVhLotteryParticipantModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhLotteryParticipantRows).From(m.table)
}

func (m *defaultVhLotteryParticipantModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhLotteryParticipantModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhLotteryParticipantModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryParticipantModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryParticipantModel) TableName() string {
	return m.table
}
