package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ UserLevelTaskModel = (*customUserLevelTaskModel)(nil)

type (
	// UserLevelTaskModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUserLevelTaskModel.
	UserLevelTaskModel interface {
		userLevelTaskModel
	}

	customUserLevelTaskModel struct {
		*defaultUserLevelTaskModel
	}
)

// NewUserLevelTaskModel returns a model for the database table.
func NewUserLevelTaskModel(conn sqlx.SqlConn) UserLevelTaskModel {
	return &customUserLevelTaskModel{
		defaultUserLevelTaskModel: newUserLevelTaskModel(conn),
	}
}
