// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	userAddressFieldNames          = builder.RawFieldNames(&UserAddress{})
	userAddressRows                = strings.Join(userAddressFieldNames, ",")
	userAddressRowsExpectAutoSet   = strings.Join(stringx.Remove(userAddressFieldNames, "`id`", "`created_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	userAddressRowsWithPlaceHolder = strings.Join(stringx.Remove(userAddressFieldNames, "`id`", "`created_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	userAddressModel interface {
		Insert(ctx context.Context, data *UserAddress) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserAddress, error)
		Update(ctx context.Context, data *UserAddress) error
		Delete(ctx context.Context, id int64) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
	}

	defaultUserAddressModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UserAddress struct {
		Id             int64     `db:"id"`
		Uid            int64     `db:"uid"`             // 用户id
		ProvinceId     int64     `db:"province_id"`     // 省id
		CityId         int64     `db:"city_id"`         // 市id
		TownId         int64     `db:"town_id"`         // 区id
		Address        string    `db:"address"`         // 详细地址
		IsDefault      int64     `db:"is_default"`      // 是否设为默认:1=是,0=否
		Label          string    `db:"label"`           // 标签
		Code           string    `db:"code"`            // 邮编
		Consignee      string    `db:"consignee"`       // 收货人
		ConsigneePhone string    `db:"consignee_phone"` // 手机号码
		ProvinceName   string    `db:"province_name"`   // 省名称
		CityName       string    `db:"city_name"`       // 市名称
		TownName       string    `db:"town_name"`       // 区名称
		CreatedTime    time.Time `db:"created_time"`    // 创建时间
		UpdateTime     time.Time `db:"update_time"`     // 更新时间
	}
)

func newUserAddressModel(conn sqlx.SqlConn) *defaultUserAddressModel {
	return &defaultUserAddressModel{
		conn:  conn,
		table: "`vh_user_address`",
	}
}

func (m *defaultUserAddressModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUserAddressModel) FindOne(ctx context.Context, id int64) (*UserAddress, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userAddressRows, m.table)
	var resp UserAddress
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserAddressModel) Insert(ctx context.Context, data *UserAddress) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, userAddressRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.ProvinceId, data.CityId, data.TownId, data.Address, data.IsDefault, data.Label, data.Code, data.Consignee, data.ConsigneePhone, data.ProvinceName, data.CityName, data.TownName)
	return ret, err
}

func (m *defaultUserAddressModel) Update(ctx context.Context, data *UserAddress) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userAddressRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.ProvinceId, data.CityId, data.TownId, data.Address, data.IsDefault, data.Label, data.Code, data.Consignee, data.ConsigneePhone, data.ProvinceName, data.CityName, data.TownName, data.Id)
	return err
}

func (m *defaultUserAddressModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(userAddressRows).From(m.table)
}

func (m *defaultUserAddressModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultUserAddressModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserAddressModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultUserAddressModel) TableName() string {
	return m.table
}
