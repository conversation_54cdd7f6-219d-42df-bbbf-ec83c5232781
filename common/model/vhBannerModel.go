package model

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ BannerModel = (*customBannerModel)(nil)

type (
	// BannerModel is an interface to be customized, add more methods here,
	// and implement the added methods in customBannerModel.
	BannerModel interface {
		bannerModel
		ClearCache(ctx context.Context, id int64) error
	}

	customBannerModel struct {
		*defaultBannerModel
	}
)

// NewBannerModel returns a model for the database table.
func NewBannerModel(conn sqlx.SqlConn, c cache.CacheConf) BannerModel {
	return &customBannerModel{
		defaultBannerModel: newBannerModel(conn, c),
	}
}

// ClearCache clears the cache for a specific banner
func (m *customBannerModel) ClearCache(ctx context.Context, id int64) error {
	key := fmt.Sprintf("%s%v", cacheMulandoBannerIdPrefix, id)
	return m.Del<PERSON>ache(key)
}
