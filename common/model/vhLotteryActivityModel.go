package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhLotteryActivityModel = (*customVhLotteryActivityModel)(nil)

type (
	// VhLotteryActivityModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhLotteryActivityModel.
	VhLotteryActivityModel interface {
		vhLotteryActivityModel
	}

	customVhLotteryActivityModel struct {
		*defaultVhLotteryActivityModel
	}
)

// NewVhLotteryActivityModel returns a model for the database table.
func NewVhLotteryActivityModel(conn sqlx.SqlConn) VhLotteryActivityModel {
	return &customVhLotteryActivityModel{
		defaultVhLotteryActivityModel: newVhLotteryActivityModel(conn),
	}
}
