package model

import "github.com/zeromicro/go-zero/core/stores/sqlx"

var _ VhLotteryParticipantModel = (*customVhLotteryParticipantModel)(nil)

type (
	// VhLotteryParticipantModel is an interface to be customized, add more methods here,
	// and implement the added methods in customVhLotteryParticipantModel.
	VhLotteryParticipantModel interface {
		vhLotteryParticipantModel
	}

	customVhLotteryParticipantModel struct {
		*defaultVhLotteryParticipantModel
	}
)

// NewVhLotteryParticipantModel returns a model for the database table.
func NewVhLotteryParticipantModel(conn sqlx.SqlConn) VhLotteryParticipantModel {
	return &customVhLotteryParticipantModel{
		defaultVhLotteryParticipantModel: newVhLotteryParticipantModel(conn),
	}
}
