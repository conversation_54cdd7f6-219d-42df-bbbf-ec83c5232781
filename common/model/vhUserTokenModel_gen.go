// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	userTokenFieldNames          = builder.RawFieldNames(&UserToken{})
	userTokenRows                = strings.Join(userTokenFieldNames, ",")
	userTokenRowsExpectAutoSet   = strings.Join(stringx.Remove(userTokenFieldNames, "`id`", "`created_time`", "`create_at`", "`update_at`"), ",")
	userTokenRowsWithPlaceHolder = strings.Join(stringx.Remove(userTokenFieldNames, "`id`", "`created_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	userTokenModel interface {
		Insert(ctx context.Context, data *UserToken) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserToken, error)
		FindOneByToken(ctx context.Context, token string) (*UserToken, error)
		FindOneByUid(ctx context.Context, uid int64) (*UserToken, error)
		Update(ctx context.Context, data *UserToken) error
		Delete(ctx context.Context, id int64) error
		DeleteByUid(ctx context.Context, uid int64) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
	}

	defaultUserTokenModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UserToken struct {
		Id          int64     `db:"id"`
		Uid         int64     `db:"uid"`          // 用户ID
		Token       string    `db:"token"`        // token
		ExpireTime  int64     `db:"expire_time"`  // 过期时间
		IsInvalid   int64     `db:"is_invalid"`   // 是否失效:0=否,1=是
		CreatedTime time.Time `db:"created_time"` // 创建时间
	}
)

func newUserTokenModel(conn sqlx.SqlConn) *defaultUserTokenModel {
	return &defaultUserTokenModel{
		conn:  conn,
		table: "`vh_user_token`",
	}
}

func (m *defaultUserTokenModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUserTokenModel) DeleteByUid(ctx context.Context, uid int64) error {
	query := fmt.Sprintf("delete from %s where `uid` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, uid)
	return err
}

func (m *defaultUserTokenModel) FindOne(ctx context.Context, id int64) (*UserToken, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userTokenRows, m.table)
	var resp UserToken
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserTokenModel) FindOneByToken(ctx context.Context, token string) (*UserToken, error) {
	query := fmt.Sprintf("select %s from %s where `token` = ? limit 1", userTokenRows, m.table)
	var resp UserToken
	err := m.conn.QueryRowCtx(ctx, &resp, query, token)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserTokenModel) FindOneByUid(ctx context.Context, uid int64) (*UserToken, error) {
	query := fmt.Sprintf("select %s from %s where `uid` = ? limit 1", userTokenRows, m.table)
	var resp UserToken
	err := m.conn.QueryRowCtx(ctx, &resp, query, uid)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserTokenModel) Insert(ctx context.Context, data *UserToken) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, userTokenRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Uid, data.Token, data.ExpireTime, data.IsInvalid)
	return ret, err
}

func (m *defaultUserTokenModel) Update(ctx context.Context, data *UserToken) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userTokenRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Uid, data.Token, data.ExpireTime, data.IsInvalid, data.Id)
	return err
}

func (m *defaultUserTokenModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(userTokenRows).From(m.table)
}

func (m *defaultUserTokenModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultUserTokenModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserTokenModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultUserTokenModel) TableName() string {
	return m.table
}
