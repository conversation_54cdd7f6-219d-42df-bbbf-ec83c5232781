// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	vhLotteryActivityFieldNames          = builder.RawFieldNames(&VhLotteryActivity{})
	vhLotteryActivityRows                = strings.Join(vhLotteryActivityFieldNames, ",")
	vhLotteryActivityRowsExpectAutoSet   = strings.Join(stringx.Remove(vhLotteryActivityFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	vhLotteryActivityRowsWithPlaceHolder = strings.Join(stringx.Remove(vhLotteryActivityFieldNames, "`id`", "`create_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	vhLotteryActivityModel interface {
		Insert(ctx context.Context, data *VhLotteryActivity) (sql.Result, error)
		InsertTx(ctx context.Context, tx *sql.Tx, data *VhLotteryActivity) (sql.Result, error)
		FindOne(ctx context.Context, id uint64) (*VhLotteryActivity, error)
		Update(ctx context.Context, data *VhLotteryActivity) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
		UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error)
		Delete(ctx context.Context, id uint64) error
	}

	defaultVhLotteryActivityModel struct {
		conn  sqlx.SqlConn
		table string
	}

	VhLotteryActivity struct {
		Id               uint64    `db:"id"`
		Title            string    `db:"title"`             // 活动名称
		Describe         string    `db:"describe"`          // 活动描述
		Status           uint64    `db:"status"`            // 活动状态（0禁用，1开启，2已开奖）
		StartTime        time.Time `db:"start_time"`        // 开始时间
		GoodsId          uint64    `db:"goods_id"`          // 关联的商品id
		GoodsTitle       string    `db:"goods_title"`       // 活动商品标题
		GoodsImg         string    `db:"goods_img"`         // 活动商品图片
		Total            uint64    `db:"total"`             // 开奖人数门槛
		WinnerCount      uint64    `db:"winner_count"`      // 中奖人数配额
		ParticipantCount uint64    `db:"participant_count"` // 已参加人数
		CreateTime       time.Time `db:"create_time"`       // 创建时间
	}
)

func newVhLotteryActivityModel(conn sqlx.SqlConn) *defaultVhLotteryActivityModel {
	return &defaultVhLotteryActivityModel{
		conn:  conn,
		table: "`vh_lottery_activity`",
	}
}

func (m *defaultVhLotteryActivityModel) Delete(ctx context.Context, id uint64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultVhLotteryActivityModel) FindOne(ctx context.Context, id uint64) (*VhLotteryActivity, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", vhLotteryActivityRows, m.table)
	var resp VhLotteryActivity
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryActivityModel) Insert(ctx context.Context, data *VhLotteryActivity) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhLotteryActivityRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Title, data.Describe, data.Status, data.StartTime, data.GoodsId, data.GoodsTitle, data.GoodsImg, data.Total, data.WinnerCount, data.ParticipantCount)
	return ret, err
}

func (m *defaultVhLotteryActivityModel) InsertTx(ctx context.Context, tx *sql.Tx, data *VhLotteryActivity) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, vhLotteryActivityRowsExpectAutoSet)
	ret, err := tx.ExecContext(ctx, query, data.Title, data.Describe, data.Status, data.StartTime, data.GoodsId, data.GoodsTitle, data.GoodsImg, data.Total, data.WinnerCount, data.ParticipantCount)
	return ret, err
}

func (m *defaultVhLotteryActivityModel) Update(ctx context.Context, data *VhLotteryActivity) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, vhLotteryActivityRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Title, data.Describe, data.Status, data.StartTime, data.GoodsId, data.GoodsTitle, data.GoodsImg, data.Total, data.WinnerCount, data.ParticipantCount, data.Id)
	return err
}

func (m *defaultVhLotteryActivityModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(vhLotteryActivityRows).From(m.table)
}

func (m *defaultVhLotteryActivityModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultVhLotteryActivityModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}
	var count int64

	err = m.conn.QueryRowCtx(ctx, &count, query, values...)

	switch err {
	case nil:
		return count, nil
	default:
		return 0, err
	}
}

func (m *defaultVhLotteryActivityModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryActivityModel) UpdateCustomTx(ctx context.Context, tx *sql.Tx, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}
	result, err := tx.ExecContext(ctx, query, values...)
	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultVhLotteryActivityModel) TableName() string {
	return m.table
}
