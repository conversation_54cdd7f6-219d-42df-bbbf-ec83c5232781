// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	userLevelTaskFieldNames          = builder.RawFieldNames(&UserLevelTask{})
	userLevelTaskRows                = strings.Join(userLevelTaskFieldNames, ",")
	userLevelTaskRowsExpectAutoSet   = strings.Join(stringx.Remove(userLevelTaskFieldNames, "`id`", "`create_at`", "`update_at`"), ",")
	userLevelTaskRowsWithPlaceHolder = strings.Join(stringx.Remove(userLevelTaskFieldNames, "`id`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	userLevelTaskModel interface {
		Insert(ctx context.Context, data *UserLevelTask) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserLevelTask, error)
		Update(ctx context.Context, data *UserLevelTask) error
		Delete(ctx context.Context, id int64) error
		DeleteByLevel(ctx context.Context, level int64) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
	}

	defaultUserLevelTaskModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UserLevelTask struct {
		Id    int64 `db:"id"`
		Level int64 `db:"level"` // 会员等级
		Name  string `db:"name"`  // 任务名称
		Type  int64 `db:"type"`  // 类型:1=分享数,2=考试
		Num   int64 `db:"num"`   // 完成数量
	}
)

func newUserLevelTaskModel(conn sqlx.SqlConn) *defaultUserLevelTaskModel {
	return &defaultUserLevelTaskModel{
		conn:  conn,
		table: "`vh_user_level_task`",
	}
}

func (m *defaultUserLevelTaskModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUserLevelTaskModel) DeleteByLevel(ctx context.Context, level int64) error {
	query := fmt.Sprintf("delete from %s where `level` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, level)
	return err
}

func (m *defaultUserLevelTaskModel) FindOne(ctx context.Context, id int64) (*UserLevelTask, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userLevelTaskRows, m.table)
	var resp UserLevelTask
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserLevelTaskModel) Insert(ctx context.Context, data *UserLevelTask) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, userLevelTaskRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Level, data.Name, data.Type, data.Num)
	return ret, err
}

func (m *defaultUserLevelTaskModel) Update(ctx context.Context, data *UserLevelTask) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userLevelTaskRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Level, data.Name, data.Type, data.Num, data.Id)
	return err
}

func (m *defaultUserLevelTaskModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(userLevelTaskRows).From(m.table)
}

func (m *defaultUserLevelTaskModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultUserLevelTaskModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserLevelTaskModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultUserLevelTaskModel) TableName() string {
	return m.table
}
