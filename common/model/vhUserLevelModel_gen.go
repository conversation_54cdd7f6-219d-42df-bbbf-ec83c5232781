// Code generated by goctl. DO NOT EDIT!

package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	userLevelFieldNames          = builder.RawFieldNames(&UserLevel{})
	userLevelRows                = strings.Join(userLevelFieldNames, ",")
	userLevelRowsExpectAutoSet   = strings.Join(stringx.Remove(userLevelFieldNames, "`id`", "`created_time`", "`update_time`", "`create_at`", "`update_at`"), ",")
	userLevelRowsWithPlaceHolder = strings.Join(stringx.Remove(userLevelFieldNames, "`id`", "`created_time`", "`update_time`", "`create_at`", "`update_at`"), "=?,") + "=?"
)

type (
	userLevelModel interface {
		Insert(ctx context.Context, data *UserLevel) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserLevel, error)
		FindOneByLevel(ctx context.Context, level int64) (*UserLevel, error)
		Update(ctx context.Context, data *UserLevel) error
		Delete(ctx context.Context, id int64) error
		TableName() string
		RowBuilder() squirrel.SelectBuilder
		FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error)
	}

	defaultUserLevelModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UserLevel struct {
		Id               int64     `db:"id"`
		Level            int64     `db:"level"`            // 合伙人等级
		Name             string    `db:"name"`             // 名称
		LevelName        string    `db:"level_name"`       // 展示名称
		ShareBrokerage   float64   `db:"share_brokerage"`  // 分享返佣比例(单位%)
		PaymentBrokerage float64   `db:"payment_brokerage"` // 购买返佣比例(单位%)
		CashBrokerage    float64   `db:"cash_brokerage"`   // 提现佣金比例(单位%)
		CashMinAmount    float64   `db:"cash_min_amount"`  // 提现最低金额(单位元)
		Duration         int64     `db:"duration"`         // 升级后持续时间(单位天)
		CreatedTime      time.Time `db:"created_time"`     // 创建时间
		UpdateTime       time.Time `db:"update_time"`      // 更新时间
	}
)

func newUserLevelModel(conn sqlx.SqlConn) *defaultUserLevelModel {
	return &defaultUserLevelModel{
		conn:  conn,
		table: "`vh_user_level`",
	}
}

func (m *defaultUserLevelModel) Delete(ctx context.Context, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUserLevelModel) FindOne(ctx context.Context, id int64) (*UserLevel, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userLevelRows, m.table)
	var resp UserLevel
	err := m.conn.QueryRowCtx(ctx, &resp, query, id)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserLevelModel) FindOneByLevel(ctx context.Context, level int64) (*UserLevel, error) {
	query := fmt.Sprintf("select %s from %s where `level` = ? limit 1", userLevelRows, m.table)
	var resp UserLevel
	err := m.conn.QueryRowCtx(ctx, &resp, query, level)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserLevelModel) Insert(ctx context.Context, data *UserLevel) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, userLevelRowsExpectAutoSet)
	ret, err := m.conn.ExecCtx(ctx, query, data.Level, data.Name, data.LevelName, data.ShareBrokerage, data.PaymentBrokerage, data.CashBrokerage, data.CashMinAmount, data.Duration)
	return ret, err
}

func (m *defaultUserLevelModel) Update(ctx context.Context, data *UserLevel) error {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userLevelRowsWithPlaceHolder)
	_, err := m.conn.ExecCtx(ctx, query, data.Level, data.Name, data.LevelName, data.ShareBrokerage, data.PaymentBrokerage, data.CashBrokerage, data.CashMinAmount, data.Duration, data.Id)
	return err
}

func (m *defaultUserLevelModel) RowBuilder() squirrel.SelectBuilder {
	return squirrel.Select(userLevelRows).From(m.table)
}

func (m *defaultUserLevelModel) FindRows(ctx context.Context, builder squirrel.SelectBuilder, resp interface{}) error {
	query, values, err := builder.ToSql()
	if err != nil {
		return err
	}

	err = m.conn.QueryRowsCtx(ctx, resp, query, values...)

	switch err {
	case nil:
		return nil
	default:
		return err
	}
}

func (m *defaultUserLevelModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)

	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserLevelModel) UpdateCustom(ctx context.Context, builder squirrel.UpdateBuilder) (sql.Result, error) {
	query, values, err := builder.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := m.conn.ExecCtx(ctx, query, values...)

	switch err {
	case nil:
		return result, nil
	default:
		return nil, err
	}
}

func (m *defaultUserLevelModel) TableName() string {
	return m.table
}
