syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    FileListReq {
        Type int64 `form:"type,options=[1],default=1"`//1海报图
    }
    FileListResp {
        List []FileInfo `json:"list"`
    }

    FileInfo {
        Id int64 `json:"id"`
        Path string `json:"path"`
        Type int64 `json:"type"`
        ExtendCode string `json:"extend_code"`
    }

    FileSaveReq {
        Type int64 `json:"type,options=[1]"`//1关注,2取消关注
        Path string `json:"path" validate:"required" v:"路径"`
        ExtendCode string `json:"extend_code,optional"`
    }

    FileDeleteReq {
        IdJ
    }

    ImgReSizeReq {
        Imgs []string `json:"imgs"`
        ResizeWidth int64 `json:"resize_width,default=800"`
    }

    ImgReSizeResp {
        Imgs []Img `json:"imgs"`
    }
    Img {
        Path string `json:"path"`
        ByteSize int64 `json:"byte_size"`
    }
)

@server(
    middleware: Global,Auth
    group: file
    prefix: /mulando_greate_destiny/v1/file
    timeout: 3s
)

service mulandoGreateDestiny {
    //文件列表
    @handler List
    get /list (FileListReq) returns (FileListResp)

    //保存文件
    @handler Save
    post /save (FileSaveReq)

    //删除文件
    @handler Delete
    post /delete (FileDeleteReq)
}