syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    // Banner信息
    BannerInfo {
        Id int64 `json:"id"`
        Picture string `json:"picture"`
        JumpType int64 `json:"jump_type"`
        IssuedTime string `json:"issued_time"`
        SoldTime string `json:"sold_time"`
        Sort int64 `json:"sort"`
        Status int64 `json:"status"`
        CreatedTime string `json:"created_time"`
        UpdateTime string `json:"update_time"`
        VhUid int64 `json:"vh_uid"`
        VhVosName string `json:"vh_vos_name"`
        JumpValue string `json:"jump_value"`
    }

    // 小程序Banner信息
    MiniProgramBannerInfo {
        Id int64 `json:"id"`
        Picture string `json:"picture"`
        JumpType int64 `json:"jump_type"`
        JumpValue string `json:"jump_value"`
    }

    // 后台Banner列表请求
    AdminBannerListReq {
        Paging
        Status int64 `form:"status,optional"` // 状态筛选，可选
    }

    // 后台Banner列表响应
    AdminBannerListResp {
        List []BannerInfo `json:"list"`
        Total int64 `json:"total"`
    }

    // 小程序Banner列表响应
    MiniProgramBannerListResp {
        List []MiniProgramBannerInfo `json:"list"`
    }

    // Banner创建请求
    BannerCreateReq {
        Picture string `json:"picture" validate:"required" v:"图片"`
        JumpType int64 `json:"jump_type" validate:"required,min=1,max=2" v:"跳转方式"`
        IssuedTime string `json:"issued_time" validate:"required" v:"上架时间"`
        SoldTime string `json:"sold_time" validate:"required" v:"下架时间"`
        Sort int64 `json:"sort,default=1" validate:"min=1" v:"排序"`
        Status int64 `json:"status,default=1" validate:"min=0,max=1" v:"状态"`
        JumpValue string `json:"jump_value,default=1" v:"跳转值"`
    }

    // Banner更新请求
    BannerUpdateReq {
        IdJ
        Picture string `json:"picture" validate:"required" v:"图片"`
        JumpType int64 `json:"jump_type" validate:"required,min=1,max=2" v:"跳转方式"`
        IssuedTime string `json:"issued_time" validate:"required" v:"上架时间"`
        SoldTime string `json:"sold_time" validate:"required" v:"下架时间"`
        Sort int64 `json:"sort" validate:"min=1" v:"排序"`
        Status int64 `json:"status" validate:"min=0,max=1" v:"状态"`
        JumpValue string `json:"jump_value" v:"跳转值"`
    }

    // Banner删除请求
    BannerDeleteReq {
        IdJ
    }

    // Banner详情请求
    BannerDetailReq {
        IdF
    }
)

// 后台Banner管理接口
@server(
    middleware: Global,Auth
    group: admin/banner
    prefix: /mulando_greate_destiny/v1/admin/banner
    timeout: 3s
)
service mulandoGreateDestiny {
    // Banner列表
    @handler AdminList
    get /list (AdminBannerListReq) returns (AdminBannerListResp)

    // Banner详情
    @handler AdminDetail
    get /detail (BannerDetailReq) returns (BannerInfo)

    // 创建Banner
    @handler AdminCreate
    post /create (BannerCreateReq)

    // 更新Banner
    @handler AdminUpdate
    post /update (BannerUpdateReq)

    // 删除Banner
    @handler AdminDelete
    post /delete (BannerDeleteReq)
}

// 小程序Banner接口
@server(
    middleware: Global
    group: miniprogram/banner
    prefix: /mulando_greate_destiny/v1/miniprogram/banner
    timeout: 3s
)
service mulandoGreateDestiny {
    // 小程序Banner列表
    @handler MiniProgramList
    get /list returns (MiniProgramBannerListResp)
}
