syntax = "v1"

info (
	title:   "mulando"
	author:  "ligenhui"
	email:   "<EMAIL>"
	version: "v3"
)

type (
	// 用户等级信息
	UserLevelInfo {
		Id               int64               `json:"id"`
		Level            int64               `json:"level"`
		Name             string              `json:"name"`
		LevelName        string              `json:"level_name"`
		ShareBrokerage   float64             `json:"share_brokerage"`
		PaymentBrokerage float64             `json:"payment_brokerage"`
		CashBrokerage    float64             `json:"cash_brokerage"`
		CashMinAmount    float64             `json:"cash_min_amount"`
		Duration         int64               `json:"duration"`
		CreatedTime      string              `json:"created_time"`
		UpdateTime       string              `json:"update_time"`
		Tasks            []UserLevelTaskInfo `json:"tasks"`
	}
	// 用户等级任务信息
	UserLevelTaskInfo {
		Id    int64  `json:"id"`
		Level int64  `json:"level"`
		Name  string `json:"name"`
		Type  int64  `json:"type"`
		Num   int64  `json:"num"`
	}
	// 后台用户等级列表请求
	AdminUserLevelListReq {
		Page   int64  `form:"page,default=1"`
		Limit  int64  `form:"limit,default=10"`
		Name   string `form:"name,optional"`
		Level  int64  `form:"level,optional"`
	}
	// 后台用户等级列表响应
	AdminUserLevelListResp {
		List  []UserLevelInfo `json:"list"`
		Total int64           `json:"total"`
	}
	// 用户等级详情请求
	UserLevelDetailReq {
		Id int64 `form:"id" validate:"required" v:"等级ID"`
	}
	// 创建用户等级请求
	UserLevelCreateReq {
		Level            int64                    `json:"level" validate:"required" v:"等级"`
		Name             string                   `json:"name" validate:"required" v:"等级名称"`
		LevelName        string                   `json:"level_name" validate:"required" v:"展示名称"`
		ShareBrokerage   float64                  `json:"share_brokerage,default=0" v:"分享返佣比例"`
		PaymentBrokerage float64                  `json:"payment_brokerage,default=0" v:"购买返佣比例"`
		CashBrokerage    float64                  `json:"cash_brokerage,default=0" v:"提现佣金比例"`
		CashMinAmount    float64                  `json:"cash_min_amount,default=0" v:"提现最低金额"`
		Duration         int64                    `json:"duration,default=0" v:"持续时间(天)"`
		Tasks            []UserLevelTaskCreateReq `json:"tasks,optional"`
	}
	// 更新用户等级请求
	UserLevelUpdateReq {
		Id               int64                    `json:"id" validate:"required" v:"等级ID"`
		Level            int64                    `json:"level" validate:"required" v:"等级"`
		Name             string                   `json:"name" validate:"required" v:"等级名称"`
		LevelName        string                   `json:"level_name" validate:"required" v:"展示名称"`
		ShareBrokerage   float64                  `json:"share_brokerage,default=0" v:"分享返佣比例"`
		PaymentBrokerage float64                  `json:"payment_brokerage,default=0" v:"购买返佣比例"`
		CashBrokerage    float64                  `json:"cash_brokerage,default=0" v:"提现佣金比例"`
		CashMinAmount    float64                  `json:"cash_min_amount,default=0" v:"提现最低金额"`
		Duration         int64                    `json:"duration,default=0" v:"持续时间(天)"`
		Tasks            []UserLevelTaskUpdateReq `json:"tasks,optional"`
	}
	// 删除用户等级请求
	UserLevelDeleteReq {
		Id int64 `json:"id" validate:"required" v:"等级ID"`
	}
	// 创建用户等级任务请求
	UserLevelTaskCreateReq {
		Name string `json:"name" validate:"required" v:"任务名称"`
		Type int64  `json:"type" validate:"required,oneof=1 2" v:"任务类型:1=分享数,2=考试"`
		Num  int64  `json:"num" validate:"required" v:"完成数量"`
	}
	// 更新用户等级任务请求
	UserLevelTaskUpdateReq {
		Id   int64  `json:"id,optional" v:"任务ID"`
		Name string `json:"name" validate:"required" v:"任务名称"`
		Type int64  `json:"type" validate:"required,oneof=1 2" v:"任务类型:1=分享数,2=考试"`
		Num  int64  `json:"num" validate:"required" v:"完成数量"`
	}
)

// 后台用户等级管理接口
@server (
	middleware: Global,Admin
	group:      admin/user_level
	prefix:     /mulando_greate_destiny/v1/admin/user_level
	timeout:    3s
)
service mulandoGreateDestiny {
	// 用户等级列表
	@handler AdminList
	get /list (AdminUserLevelListReq) returns (AdminUserLevelListResp)

	// 用户等级详情
	@handler AdminDetail
	get /detail (UserLevelDetailReq) returns (UserLevelInfo)

	// 创建用户等级
	@handler AdminCreate
	post /create (UserLevelCreateReq)

	// 更新用户等级
	@handler AdminUpdate
	post /update (UserLevelUpdateReq)

	// 删除用户等级
	@handler AdminDelete
	post /delete (UserLevelDeleteReq)
}

