syntax = "v1"

info(
    title: "mulando"
    author: "ligenhui"
    email: "<EMAIL>"
    version: "v3"
)

type (
    WeChatAppletOneclickLoginReq {
        Code string `json:"code" validate:"required" v:"临时授权码"`
        PCode string `json:"p_code" validate:"required" v:"手机号获取凭证"`
        SourceUid string `json:"source_uid,optional" validate:"omitempty" v:"来源用户ID"`
    }

    WeChatAppletOneclickLoginResp {
        UID int64 `json:"uid"`
        Token string `json:"token"`
        OpenID string `json:"openid"`
        UnionID string `json:"unionid"`
        Name string `json:"name"`
        Avatar string `json:"avatar"`
    }
)

@server(
    middleware: Global
    group: wechat
    prefix: /mulando_greate_destiny/v1/wechat
    timeout: 10s
)

service mulandoGreateDestiny {
    //微信小程序一键登录
    @handler AppletOneclickLogin
    post /applet_oneclick_login (WeChatAppletOneclickLoginReq) returns (WeChatAppletOneclickLoginResp)
}