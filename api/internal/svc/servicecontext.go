package svc

import (
	"engine/api/internal/middleware"
	cf "engine/common/config"
	"engine/common/jwtutil"
	"engine/common/model"
	"engine/common/validation"
	"time"

	"github.com/go-pay/gopay/wechat"
	red "github.com/gomodule/redigo/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config        cf.ApiConfig
	Verify        *validation.Verify
	Global        rest.Middleware
	Auth          rest.Middleware
	Admin         rest.Middleware
	ExistAuth     rest.Middleware
	Jwt           *jwtutil.JWTUtil
	Redis         *red.Pool
	UserRedis     *red.Pool //v3后台用户信息
	TransModel    model.TransModel
	WePay         *wechat.Client
	UserModel          model.VhUserModel
	UserTokenModel     model.UserTokenModel
	UserLevelModel     model.UserLevelModel
	UserLevelTaskModel model.UserLevelTaskModel
	UserAddressModel   model.UserAddressModel
	UserFileModel      model.UserFilesModel
	BannerModel        model.BannerModel
	LabelModel         model.LabelModel
}

func NewServiceContext(c cf.ApiConfig) *ServiceContext {
	mysql := sqlx.NewMysql(c.Mysql.DataSource)

	payClient := wechat.NewClient(c.WePay.AppID, c.WePay.MchId, c.WePay.ApiKey, true)
	//payClient.DebugSwitch = gopay.DebugOn
	//设置证书
	if e := payClient.AddCertPemFileContent([]byte(c.WePay.CertFileContent), []byte(c.WePay.KeyFileContent)); e != nil {
		panic(e)
	}
	srv := &ServiceContext{
		Config: c,
		Verify: validation.NewVerify(),
		Global: middleware.NewGlobalMiddleware().Handle,
		Jwt:    jwtutil.NewJWTUtil(c.Auth.AccessSecret, c.Auth.AccessExpire),
		Redis: &red.Pool{
			MaxIdle:   500,
			MaxActive: 100,
			Dial: func() (red.Conn, error) {
				return red.Dial("tcp", c.Redis[0].Host, red.DialPassword(c.Redis[0].Pass),
					red.DialDatabase(12),
					red.DialConnectTimeout(time.Second), //链接超时
					red.DialWriteTimeout(time.Second*1), //写超时
					red.DialReadTimeout(time.Second*1),  //读超时
				)
			},
		},
		UserRedis: &red.Pool{
			MaxIdle:   500,
			MaxActive: 20,
			Dial: func() (red.Conn, error) {
				return red.Dial("tcp", c.Redis[0].Host, red.DialPassword(c.Redis[0].Pass),
					red.DialDatabase(11),
					red.DialConnectTimeout(time.Second), //链接超时
					red.DialWriteTimeout(time.Second*1), //写超时
					red.DialReadTimeout(time.Second*1),  //读超时
				)
			},
		},
		TransModel:         model.NewTransModel(mysql),
		WePay:              payClient,
		UserModel:          model.NewVhUserModel(mysql, c.Redis),
		UserTokenModel:     model.NewUserTokenModel(mysql),
		UserLevelModel:     model.NewUserLevelModel(mysql),
		UserLevelTaskModel: model.NewUserLevelTaskModel(mysql),
		UserAddressModel:   model.NewUserAddressModel(mysql),
		UserFileModel:      model.NewUserFilesModel(mysql),
		BannerModel:        model.NewBannerModel(mysql, c.Redis),
		LabelModel:         model.NewLabelModel(mysql, c.Redis),
	}
	srv.Auth = middleware.NewAuthMiddleware(srv.Jwt, srv.UserTokenModel).Handle
	srv.Admin = middleware.NewAdminMiddleware(srv.UserRedis).Handle
	srv.ExistAuth = middleware.NewExistAuthMiddleware(srv.Jwt).Handle

	return srv
}

func (c *ServiceContext) Close() {
}
