package lotteryActivity

import (
	"engine/api/internal/logic/miniprogram/lotteryActivity"
	"engine/api/internal/svc"
	"engine/common/result"
	"net/http"
)

func ParticipantHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := lotteryActivity.NewParticipantLogic(r.Context(), svcCtx)
		resp, err := l.Participant()
		result.HttpResult(r, w, resp, err)
	}
}
