// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"
	"time"

	adminbanner "engine/api/internal/handler/admin/banner"
	adminlabel "engine/api/internal/handler/admin/label"
	adminlotteryActivity "engine/api/internal/handler/admin/lotteryActivity"
	adminuser "engine/api/internal/handler/admin/user"
	adminuser_level "engine/api/internal/handler/admin/user_level"
	file "engine/api/internal/handler/file"
	miniprogramaddress "engine/api/internal/handler/miniprogram/address"
	miniprogrambanner "engine/api/internal/handler/miniprogram/banner"
	miniprogramlotteryActivity "engine/api/internal/handler/miniprogram/lotteryActivity"
	miniprogramuser "engine/api/internal/handler/miniprogram/user"
	pay "engine/api/internal/handler/pay"
	wechat "engine/api/internal/handler/wechat"
	"engine/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminbanner.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminbanner.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminbanner.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminbanner.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminbanner.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/admin/banner"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminlabel.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminlabel.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminlabel.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminlabel.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminlabel.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/admin/label"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminlotteryActivity.CreateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminlotteryActivity.ListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminlotteryActivity.UpdateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/updateStatus",
					Handler: adminlotteryActivity.UpdateStatusHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/admin/lotteryActivity"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminuser.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/set_disabled",
					Handler: adminuser.SetDisabledHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/admin/user"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Admin},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: adminuser_level.AdminCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: adminuser_level.AdminDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: adminuser_level.AdminDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: adminuser_level.AdminListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: adminuser_level.AdminUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/admin/user_level"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: file.DeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: file.ListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/save",
					Handler: file.SaveHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/file"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/create",
					Handler: miniprogramaddress.AddressCreateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/delete",
					Handler: miniprogramaddress.AddressDeleteHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/detail",
					Handler: miniprogramaddress.AddressDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: miniprogramaddress.AddressListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/update",
					Handler: miniprogramaddress.AddressUpdateHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/miniprogram/address"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/list",
					Handler: miniprogrambanner.MiniProgramListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/miniprogram/banner"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.ExistAuth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/active",
					Handler: miniprogramlotteryActivity.GetActiveHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/lotteryActivity"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/participant",
					Handler: miniprogramlotteryActivity.ParticipantHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/miniprogram/lotteryActivity"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/wechat_login",
					Handler: miniprogramuser.WeChatLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/miniprogram/user"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global, serverCtx.Auth},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/info",
					Handler: miniprogramuser.GetUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/logout",
					Handler: miniprogramuser.LogoutHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/miniprogram/user"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/we_callback",
					Handler: pay.WeCallbackHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulandoGreateDestiny/v1/pay"),
		rest.WithTimeout(3000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Global},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/applet_oneclick_login",
					Handler: wechat.AppletOneclickLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/mulando_greate_destiny/v1/wechat"),
		rest.WithTimeout(10000*time.Millisecond),
	)
}
