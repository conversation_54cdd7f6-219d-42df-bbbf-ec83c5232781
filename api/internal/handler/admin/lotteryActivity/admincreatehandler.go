package lotteryActivity

import (
	"engine/api/internal/logic/admin/lotteryActivity"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/result"
	"net/http"
)

func AdminCreateHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.LotteryActivityCreateReq
		if err := httpx.Parse(r, &req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		if err := svcCtx.Verify.Struct(&req); err != nil {
			result.ParamErrorResult(r, w, err)
			return
		}

		l := lotteryActivity.NewAdminCreateLogic(r.Context(), svcCtx)
		err := l.AdminCreate(&req)
		result.HttpResult(r, w, result.NullJson{}, err)
	}
}
