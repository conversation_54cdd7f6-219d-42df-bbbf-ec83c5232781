package wechat

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/wechat"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AppletOneclickLoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAppletOneclickLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AppletOneclickLoginLogic {
	return &AppletOneclickLoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AppletOneclickLoginLogic) AppletOneclickLogin(req *types.WeChatAppletOneclickLoginReq) (resp *types.WeChatAppletOneclickLoginResp, err error) {
	var result types.WeChatAppletOneclickLoginResp

	t := time.Now()
	resp = &result
	w_service := wechat.NewWeChatService(l.svcCtx.Config)

	// 获取微信用户openid
	openidRes, _ := w_service.GetOpenid(req.Code)
	if openidRes.Openid == "" {
		return resp, xerr.NewErrMsg(openidRes.Msg)
	}
	// 获取用户手机号
	phoneRes, _ := w_service.GetUserPhonenumber(req.PCode)
	if phoneRes.PhoneInfo.PurePhoneNumber == "" {
		return resp, xerr.NewErrMsg(phoneRes.ErrMsg)
	}
	telephone := phoneRes.PhoneInfo.PurePhoneNumber

	user, err := l.svcCtx.UserModel.FindOneByTelephone(l.ctx, telephone)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("note add userModel.findOne error: %v", err)
		return resp, xerr.NewErrCode(xerr.DbError)
	}

	if user == nil || user.Id == 0 { // 创建新用户
		user = &model.VhUser{
			Nickname:     fmt.Sprintf("WineNotes-%d", rand.Intn(999999)+1000000),
			AvatarImage:  "/avatars/vinotes.png",
			Telephone:    telephone,
			AppletOpenid: openidRes.Openid,
			//Unionid:   openidRes.Unionid,
		}
		r, err := l.svcCtx.UserModel.Insert(l.ctx, user)
		if err != nil {
			return resp, xerr.NewErrMsg(err.Error())
		}
		uid, err := r.LastInsertId()
		if err != nil {
			return resp, xerr.NewErrMsg(err.Error())
		}
		user.Id = uid

		// 邀请新人

	} else {
		// 更新登录时间
		user.LastLoginTime = t.Unix()
		user.AppletOpenid = openidRes.Openid
		//user.Unionid = openidRes.Unionid
		_ = l.svcCtx.UserModel.Update(l.ctx, user)
	}

	// 生成登录token
	token, err := l.svcCtx.Jwt.GenerateToken(map[string]interface{}{
		"uid":       user.Id,
		"telephone": user.Telephone,
		"time":      int64(time.Now().Unix()),
	})
	if err != nil {
		return resp, xerr.NewErrMsg(err.Error())
	}

	// 保存token到数据库
	expireTime := t.Add(l.svcCtx.Config.Auth.AccessExpire).Unix()

	// 删除旧token
	_ = l.svcCtx.UserTokenModel.DeleteByUid(l.ctx, user.Id)

	// 插入新token
	userToken := &model.UserToken{
		Uid:        user.Id,
		Token:      token,
		ExpireTime: expireTime,
		IsInvalid:  0, // 0=有效
	}

	_, err = l.svcCtx.UserTokenModel.Insert(l.ctx, userToken)
	if err != nil {
		l.Logger.Error("AppletOneclickLogin UserTokenModel.Insert error: %v", err)
		// 这里不返回错误，因为主要功能已经完成
	}

	// 头像
	if user.AvatarImage != "" && !strings.Contains(user.AvatarImage, "http") {
		user.AvatarImage = l.svcCtx.Config.ITEM.ALIURL + user.AvatarImage
	}

	result = types.WeChatAppletOneclickLoginResp{
		UID:    user.Id,
		Token:  token,
		OpenID: user.AppletOpenid,
		//UnionID: user.Unionid,
		Name:   user.Nickname,
		Avatar: user.AvatarImage,
	}

	return
}
