package user

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetDisabledLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSetDisabledLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetDisabledLogic {
	return &SetDisabledLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetDisabledLogic) SetDisabled(req *types.UserSetDisabledReq) error {
	// 检查用户是否存在
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("SetDisabled UserModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 更新用户禁用状态（vh_user表中没有vh_uid和vh_vos_name字段）
	updateBuilder := squirrel.Update(l.svcCtx.UserModel.TableName()).
		Set("is_disabled", req.IsDisabled).
		Where(squirrel.Eq{"id": req.Id})

	_, err = l.svcCtx.UserModel.UpdateCustom(l.ctx, updateBuilder)
	if err != nil {
		l.Logger.Error("SetDisabled UserModel.UpdateCustom error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 如果禁用用户，删除其token
	if req.IsDisabled == 1 {
		err = l.svcCtx.UserTokenModel.DeleteByUid(l.ctx, user.Id)
		if err != nil && err != model.ErrNotFound {
			l.Logger.Error("SetDisabled UserTokenModel.DeleteByUid error: %v", err)
			// 这里不返回错误，因为主要操作已经成功
		}
	}

	return nil
}
