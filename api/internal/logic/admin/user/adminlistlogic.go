package user

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminListLogic {
	return &AdminListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminListLogic) AdminList(req *types.AdminUserListReq) (resp *types.AdminUserListResp, err error) {
	// 构建查询条件
	builder := l.svcCtx.UserModel.RowBuilder()
	
	// 添加筛选条件
	if req.Telephone != "" {
		builder = builder.Where(squirrel.Like{"telephone": "%" + req.Telephone + "%"})
	}
	if req.Nickname != "" {
		builder = builder.Where(squirrel.Like{"nickname": "%" + req.Nickname + "%"})
	}
	if req.Level > 0 {
		builder = builder.Where(squirrel.Eq{"level": req.Level})
	}
	if req.Type > 0 {
		builder = builder.Where(squirrel.Eq{"type": req.Type})
	}
	if req.IsDisabled >= 0 {
		builder = builder.Where(squirrel.Eq{"is_disabled": req.IsDisabled})
	}

	// 获取总数
	countBuilder := model.CountBuilder("id", l.svcCtx.UserModel.TableName())
	if req.Telephone != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"telephone": "%" + req.Telephone + "%"})
	}
	if req.Nickname != "" {
		countBuilder = countBuilder.Where(squirrel.Like{"nickname": "%" + req.Nickname + "%"})
	}
	if req.Level > 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"level": req.Level})
	}
	if req.Type > 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"type": req.Type})
	}
	if req.IsDisabled >= 0 {
		countBuilder = countBuilder.Where(squirrel.Eq{"is_disabled": req.IsDisabled})
	}

	total, err := l.svcCtx.UserModel.FindCount(l.ctx, countBuilder)
	if err != nil {
		l.Logger.Error("AdminList UserModel.FindCount error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 分页查询
	builder = builder.OrderBy("id DESC").
		Limit(uint64(req.Limit)).
		Offset(model.GetOffset(req.Page, req.Limit))

	var users []*model.VhUser
	err = l.svcCtx.UserModel.FindRows(l.ctx, builder, &users)
	if err != nil {
		l.Logger.Error("AdminList UserModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换数据
	list := make([]types.UserInfo, 0, len(users))
	for _, user := range users {
		userInfo := types.UserInfo{
			Id:             user.Id,
			AppletOpenid:   user.AppletOpenid,
			Telephone:      user.Telephone,
			Nickname:       user.Nickname,
			AvatarImage:    user.AvatarImage,
			Sex:            user.Sex,
			Birthday:       user.Birthday.String,
			IsDisabled:     user.IsDisabled,
			Balance:        user.Balance,
			Level:          user.Level,
			LastLoginTime:  common.TimeToString(common.UnixToTime(user.LastLoginTime)),
			CreatedTime:    common.TimeToString(user.CreatedTime),
			UpdateTime:     common.TimeToString(user.UpdateTime),
			TotalBalance:   user.TotalBalance,
			LevelStartTime: common.TimeToString(common.UnixToTime(user.LevelStartTime)),
			LevelEndTime:   common.TimeToString(common.UnixToTime(user.LevelEndTime)),
			Type:           user.Type,
		}
		list = append(list, userInfo)
	}

	return &types.AdminUserListResp{
		List:  list,
		Total: total,
	}, nil
}
