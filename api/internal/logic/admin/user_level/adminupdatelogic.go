package user_level

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminUpdateLogic {
	return &AdminUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminUpdateLogic) AdminUpdate(req *types.UserLevelUpdateReq) error {
	// 获取管理员信息 - 暂时不使用，因为新表结构中没有这些字段
	// adminUid := l.ctx.Value("admin_uid").(int64)
	// adminVosName := l.ctx.Value("admin_vos_name").(string)

	// 检查用户等级是否存在
	userLevel, err := l.svcCtx.UserLevelModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminUpdate UserLevelModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 如果等级值发生变化，检查新等级是否已存在
	if req.Level != userLevel.Level {
		existLevel, err := l.svcCtx.UserLevelModel.FindOneByLevel(l.ctx, req.Level)
		if err != nil && err != model.ErrNotFound {
			l.Logger.Error("AdminUpdate UserLevelModel.FindOneByLevel error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
		if existLevel != nil {
			return xerr.NewErrMsg("该等级已存在")
		}
	}

	// 更新用户等级 - 使用正确的字段结构
	userLevel.Level = req.Level
	userLevel.Name = req.Name
	userLevel.LevelName = req.LevelName
	userLevel.ShareBrokerage = req.ShareBrokerage
	userLevel.PaymentBrokerage = req.PaymentBrokerage
	userLevel.CashBrokerage = req.CashBrokerage
	userLevel.CashMinAmount = req.CashMinAmount
	userLevel.Duration = req.Duration

	err = l.svcCtx.UserLevelModel.Update(l.ctx, userLevel)
	if err != nil {
		l.Logger.Error("AdminUpdate UserLevelModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 删除原有的任务
	err = l.svcCtx.UserLevelTaskModel.DeleteByLevel(l.ctx, userLevel.Level)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("AdminUpdate UserLevelTaskModel.DeleteByLevel error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 创建新的任务 - 使用正确的字段结构
	for _, taskReq := range req.Tasks {
		task := &model.UserLevelTask{
			Level: req.Level,
			Name:  taskReq.Name,
			Type:  taskReq.Type,
			Num:   taskReq.Num,
		}

		_, err = l.svcCtx.UserLevelTaskModel.Insert(l.ctx, task)
		if err != nil {
			l.Logger.Error("AdminUpdate UserLevelTaskModel.Insert error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	return nil
}
