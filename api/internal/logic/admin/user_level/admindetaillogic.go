package user_level

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDetailLogic {
	return &AdminDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDetailLogic) AdminDetail(req *types.UserLevelDetailReq) (resp *types.UserLevelInfo, err error) {
	// 查询用户等级信息
	userLevel, err := l.svcCtx.UserLevelModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDetail UserLevelModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 查询该等级的任务 - 按类型升序，ID升序排列
	taskBuilder := l.svcCtx.UserLevelTaskModel.RowBuilder().
		Where(squirrel.Eq{"level": userLevel.Level}).
		OrderBy("type ASC, id ASC")

	var tasks []*model.UserLevelTask
	err = l.svcCtx.UserLevelTaskModel.FindRows(l.ctx, taskBuilder, &tasks)
	if err != nil {
		l.Logger.Error("AdminDetail UserLevelTaskModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 转换任务数据 - 使用正确的字段结构
	taskInfos := make([]types.UserLevelTaskInfo, 0, len(tasks))
	for _, task := range tasks {
		taskInfo := types.UserLevelTaskInfo{
			Id:    task.Id,
			Level: task.Level,
			Name:  task.Name,
			Type:  task.Type,
			Num:   task.Num,
		}
		taskInfos = append(taskInfos, taskInfo)
	}

	// 转换用户等级数据 - 使用正确的字段结构
	userLevelInfo := &types.UserLevelInfo{
		Id:               userLevel.Id,
		Level:            userLevel.Level,
		Name:             userLevel.Name,
		LevelName:        userLevel.LevelName,
		ShareBrokerage:   userLevel.ShareBrokerage,
		PaymentBrokerage: userLevel.PaymentBrokerage,
		CashBrokerage:    userLevel.CashBrokerage,
		CashMinAmount:    userLevel.CashMinAmount,
		Duration:         userLevel.Duration,
		CreatedTime:      common.TimeToString(userLevel.CreatedTime),
		UpdateTime:       common.TimeToString(userLevel.UpdateTime),
		Tasks:            taskInfos,
	}

	return userLevelInfo, nil
}
