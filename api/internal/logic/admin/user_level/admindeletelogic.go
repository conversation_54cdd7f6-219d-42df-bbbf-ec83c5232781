package user_level

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminDeleteLogic {
	return &AdminDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminDeleteLogic) AdminDelete(req *types.UserLevelDeleteReq) error {
	// 检查用户等级是否存在
	userLevel, err := l.svcCtx.UserLevelModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminDelete UserLevelModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 删除关联的任务
	err = l.svcCtx.UserLevelTaskModel.DeleteByLevel(l.ctx, userLevel.Level)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("AdminDelete UserLevelTaskModel.DeleteByLevel error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 删除用户等级
	err = l.svcCtx.UserLevelModel.Delete(l.ctx, req.Id)
	if err != nil {
		l.Logger.Error("AdminDelete UserLevelModel.Delete error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
