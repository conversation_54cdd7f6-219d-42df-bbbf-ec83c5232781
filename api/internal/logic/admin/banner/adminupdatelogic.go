package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdminUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdminUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdminUpdateLogic {
	return &AdminUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AdminUpdateLogic) AdminUpdate(req *types.BannerUpdateReq) error {
	// 检查记录是否存在
	_, err := l.svcCtx.BannerModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AdminUpdateLogic BannerModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	// 从context中获取管理员信息
	adminUid, err := function.GetAdminUid(l.ctx)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic GetAdminUid error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	adminVosName, err := function.GetAdminVosName(l.ctx)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic GetAdminVosName error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	// 将时间字符串转换为时间戳
	issuedTime, err := function.ParseTimeString(req.IssuedTime)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic ParseTimeString IssuedTime error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	soldTime, err := function.ParseTimeString(req.SoldTime)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic ParseTimeString SoldTime error: %v", err)
		return xerr.NewErrCode(xerr.RequestParamError)
	}

	// 更新数据
	banner := &model.Banner{
		Id:         req.Id,
		Picture:    req.Picture,
		JumpType:   req.JumpType,
		IssuedTime: issuedTime,
		SoldTime:   soldTime,
		Sort:       req.Sort,
		Status:     req.Status,
		VhUid:      adminUid,      // 使用当前操作用户
		VhVosName:  adminVosName,  // 使用当前操作用户
		JumpValue:  req.JumpValue,
	}

	err = l.svcCtx.BannerModel.Update(l.ctx, banner)
	if err != nil {
		l.Logger.Error("AdminUpdateLogic BannerModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
