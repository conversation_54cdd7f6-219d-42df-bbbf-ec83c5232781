package address

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressUpdateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressUpdateLogic {
	return &AddressUpdateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressUpdateLogic) AddressUpdate(req *types.AddressUpdateReq) error {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 检查地址是否存在且属于当前用户
	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AddressUpdate UserAddressModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	if address.Uid != uid {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 如果设置为默认地址，先取消其他默认地址
	if req.IsDefault == 1 && address.IsDefault != 1 {
		updateBuilder := squirrel.Update(l.svcCtx.UserAddressModel.TableName()).
			Set("is_default", 0).
			Where(squirrel.Eq{"uid": uid, "is_default": 1})

		_, err := l.svcCtx.UserAddressModel.UpdateCustom(l.ctx, updateBuilder)
		if err != nil {
			l.Logger.Error("AddressUpdate UpdateCustom error: %v", err)
			return xerr.NewErrCode(xerr.DbError)
		}
	}

	// 更新地址 - 使用正确的字段结构
	address.ProvinceId = req.ProvinceId
	address.CityId = req.CityId
	address.TownId = req.TownId
	address.Address = req.Address
	address.IsDefault = req.IsDefault
	address.Label = req.Label
	address.Code = req.Code
	address.Consignee = req.Consignee
	address.ConsigneePhone = req.ConsigneePhone
	address.ProvinceName = req.ProvinceName
	address.CityName = req.CityName
	address.TownName = req.TownName

	err = l.svcCtx.UserAddressModel.Update(l.ctx, address)
	if err != nil {
		l.Logger.Error("AddressUpdate UserAddressModel.Update error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
