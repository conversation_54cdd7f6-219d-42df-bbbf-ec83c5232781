package address

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddressDeleteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddressDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddressDeleteLogic {
	return &AddressDeleteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddressDeleteLogic) AddressDelete(req *types.AddressDeleteReq) error {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 检查地址是否存在且属于当前用户
	address, err := l.svcCtx.UserAddressModel.FindOne(l.ctx, req.Id)
	if err != nil {
		if err == model.ErrNotFound {
			return xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("AddressDelete UserAddressModel.FindOne error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	if address.Uid != uid {
		return xerr.NewErrCode(xerr.DataNoExistError)
	}

	// 删除地址
	err = l.svcCtx.UserAddressModel.Delete(l.ctx, req.Id)
	if err != nil {
		l.Logger.Error("AddressDelete UserAddressModel.Delete error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
