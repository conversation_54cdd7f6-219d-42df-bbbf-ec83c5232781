package banner

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common/function"
	"engine/common/model"
	"engine/common/xerr"
	"github.com/Masterminds/squirrel"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type MiniProgramListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMiniProgramListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MiniProgramListLogic {
	return &MiniProgramListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MiniProgramListLogic) MiniProgramList() (resp *types.MiniProgramBannerListResp, err error) {
	var banners []*model.Banner

	now := time.Now().Unix()

	// 构建查询条件：状态启用，当前时间在上架和下架时间范围内
	// 使用 RowBuilder() 来选择所有字段，避免字段不匹配的问题
	builder := l.svcCtx.BannerModel.RowBuilder().
		Where(squirrel.Eq{"status": 1}).
		Where(squirrel.LtOrEq{"issued_time": now}).
		Where(squirrel.GtOrEq{"sold_time": now}).
		OrderBy("sort DESC, id DESC")

	// 查询数据
	err = l.svcCtx.BannerModel.FindRows(l.ctx, builder, &banners)
	if err != nil {
		l.Logger.Error("MiniProgramListLogic BannerModel.FindRows error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建响应
	resp = &types.MiniProgramBannerListResp{
		List: make([]types.MiniProgramBannerInfo, 0),
	}
	
	for _, banner := range banners {
		resp.List = append(resp.List, types.MiniProgramBannerInfo{
			Id:        banner.Id,
			Picture:   function.BuildImageURL(l.svcCtx.Config.ITEM.ALIURL, banner.Picture),
			JumpType:  banner.JumpType,
			JumpValue: banner.JumpValue,
		})
	}
	
	return
}
