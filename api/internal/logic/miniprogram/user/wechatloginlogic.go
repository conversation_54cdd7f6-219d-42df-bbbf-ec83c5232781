package user

/*
微信小程序授权登录逻辑

当前使用模拟数据进行测试，正式环境需要：
1. 取消注释真实微信API调用代码
2. 注释掉模拟数据部分
3. 确保微信小程序配置正确

模拟数据包括：
- openid: mock_openid_时间戳
- 手机号: 13800138000
- 昵称: 测试用户
- 性别: 1 (女)
- 头像: https://thirdwx.qlogo.cn/mmopen/test_avatar.jpg
*/

import (
	"context"
	"database/sql"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"
	"fmt"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type WeChatLoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWeChatLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WeChatLoginLogic {
	return &WeChatLoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type WeChatOpenidResponse struct {
	Openid     string `json:"openid"`
	SessionKey string `json:"session_key"`
	Unionid    string `json:"unionid"`
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`
}

type WeChatPhoneResponse struct {
	Errcode   int    `json:"errcode"`
	Errmsg    string `json:"errmsg"`
	PhoneInfo struct {
		PhoneNumber     string `json:"phoneNumber"`
		PurePhoneNumber string `json:"purePhoneNumber"`
		CountryCode     string `json:"countryCode"`
		Watermark       struct {
			Timestamp int64  `json:"timestamp"`
			Appid     string `json:"appid"`
		} `json:"watermark"`
	} `json:"phone_info"`
}

// 微信用户信息响应结构
type WeChatUserInfoResponse struct {
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`
	Openid     string `json:"openid"`
	Nickname   string `json:"nickname"`
	Sex        int    `json:"sex"` // 性别 0未知 1女 2男
	Province   string `json:"province"`
	City       string `json:"city"`
	Country    string `json:"country"`
	Headimgurl string `json:"headimgurl"` // 头像URL
	Unionid    string `json:"unionid"`
}

func (l *WeChatLoginLogic) WeChatLogin(req *types.WeChatLoginReq) (resp *types.WeChatLoginResp, err error) {
	// ========== 暂时使用模拟数据进行测试 ==========
	// TODO: 正式环境需要取消注释下面的真实微信API调用代码

	// 模拟微信返回的数据
	openidRes := WeChatOpenidResponse{
		Openid:     fmt.Sprintf("mock_openid_%d", time.Now().Unix()),
		SessionKey: "mock_session_key",
		Unionid:    fmt.Sprintf("mock_unionid_%d", time.Now().Unix()),
		Errcode:    0,
		Errmsg:     "",
	}

	// 模拟手机号数据
	phoneRes := WeChatPhoneResponse{
		Errcode: 0,
		Errmsg:  "",
		PhoneInfo: struct {
			PhoneNumber     string `json:"phoneNumber"`
			PurePhoneNumber string `json:"purePhoneNumber"`
			CountryCode     string `json:"countryCode"`
			Watermark       struct {
				Timestamp int64  `json:"timestamp"`
				Appid     string `json:"appid"`
			} `json:"watermark"`
		}{
			PhoneNumber:     "13800138000",
			PurePhoneNumber: "13800138000",
			CountryCode:     "86",
		},
	}

	// 模拟用户信息数据
	userInfoRes := WeChatUserInfoResponse{
		Errcode:    0,
		Errmsg:     "",
		Openid:     openidRes.Openid,
		Nickname:   "测试用户",
		Sex:        1, // 1女 2男 0未知
		Province:   "北京",
		City:       "北京",
		Country:    "中国",
		Headimgurl: "https://thirdwx.qlogo.cn/mmopen/test_avatar.jpg",
		Unionid:    openidRes.Unionid,
	}

	/* ========== 真实微信API调用代码（暂时注释） ==========
	// 1. 获取微信openid
	openidUrl := fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		l.svcCtx.Config.WeChat.AppID, l.svcCtx.Config.WeChat.AppSecret, req.Code)

	openidResp, err := http.Get(openidUrl)
	if err != nil {
		l.Logger.Error("WeChatLogin get openid error: %v", err)
		return nil, xerr.NewErrMsg("获取微信信息失败")
	}
	defer openidResp.Body.Close()

	openidBody, err := io.ReadAll(openidResp.Body)
	if err != nil {
		l.Logger.Error("WeChatLogin read openid response error: %v", err)
		return nil, xerr.NewErrMsg("获取微信信息失败")
	}

	var openidRes WeChatOpenidResponse
	err = json.Unmarshal(openidBody, &openidRes)
	if err != nil {
		l.Logger.Error("WeChatLogin unmarshal openid response error: %v", err)
		return nil, xerr.NewErrMsg("获取微信信息失败")
	}

	if openidRes.Errcode != 0 {
		l.Logger.Error("WeChatLogin openid response error: %s", openidRes.Errmsg)
		return nil, xerr.NewErrMsg("获取微信信息失败: " + openidRes.Errmsg)
	}
	*/

	/* ========== 真实获取手机号API调用代码（暂时注释） ==========
	// 2. 获取手机号
	phoneUrl := fmt.Sprintf("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s", l.getAccessToken())
	phoneData := map[string]string{"code": req.PCode}
	phoneJson, _ := json.Marshal(phoneData)

	phoneResp, err := http.Post(phoneUrl, "application/json", strings.NewReader(string(phoneJson)))
	if err != nil {
		l.Logger.Error("WeChatLogin get phone error: %v", err)
		return nil, xerr.NewErrMsg("获取手机号失败")
	}
	defer phoneResp.Body.Close()

	phoneBody, err := io.ReadAll(phoneResp.Body)
	if err != nil {
		l.Logger.Error("WeChatLogin read phone response error: %v", err)
		return nil, xerr.NewErrMsg("获取手机号失败")
	}

	var phoneRes WeChatPhoneResponse
	err = json.Unmarshal(phoneBody, &phoneRes)
	if err != nil {
		l.Logger.Error("WeChatLogin unmarshal phone response error: %v", err)
		return nil, xerr.NewErrMsg("获取手机号失败")
	}

	if phoneRes.Errcode != 0 {
		l.Logger.Error("WeChatLogin phone response error: %s", phoneRes.Errmsg)
		return nil, xerr.NewErrMsg("获取手机号失败: " + phoneRes.Errmsg)
	}
	*/

	// 从模拟数据中获取手机号
	telephone := phoneRes.PhoneInfo.PurePhoneNumber
	if telephone == "" {
		return nil, xerr.NewErrMsg("获取手机号失败")
	}

	// 3. 查找或创建用户
	user, err := l.svcCtx.UserModel.FindOneByTelephone(l.ctx, telephone)
	t := time.Now()

	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("WeChatLogin UserModel.FindOneByTelephone error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	if err == model.ErrNotFound {
		// 创建新用户 - 使用从微信授权获取的用户信息
		user = &model.VhUser{
			AppletOpenid:   openidRes.Openid,
			Telephone:      telephone,
			Nickname:       userInfoRes.Nickname,   // 从微信获取昵称
			AvatarImage:    userInfoRes.Headimgurl, // 从微信获取头像（全路径，不需要拼接ALIURL）
			Sex:            int64(userInfoRes.Sex), // 从微信获取性别 0未知 1女 2男
			Birthday:       sql.NullString{},       // 生日暂时为空，可以后续让用户补充
			IsDisabled:     0,
			Balance:        0,
			Level:          0,
			LastLoginTime:  t.Unix(),
			TotalBalance:   0,
			LevelStartTime: 0,
			LevelEndTime:   0,
			Type:           0, // 普通会员
		}

		result, err := l.svcCtx.UserModel.Insert(l.ctx, user)
		if err != nil {
			l.Logger.Error("WeChatLogin UserModel.Insert error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}

		userId, _ := result.LastInsertId()
		user.Id = userId
	} else {
		// 更新登录时间和用户信息
		user.LastLoginTime = t.Unix()
		user.AppletOpenid = openidRes.Openid
		// 更新用户信息（如果微信信息有变化）
		if userInfoRes.Nickname != "" {
			user.Nickname = userInfoRes.Nickname
		}
		if userInfoRes.Headimgurl != "" {
			user.AvatarImage = userInfoRes.Headimgurl
		}
		if userInfoRes.Sex > 0 {
			user.Sex = int64(userInfoRes.Sex)
		}

		err = l.svcCtx.UserModel.Update(l.ctx, user)
		if err != nil {
			l.Logger.Error("WeChatLogin UserModel.Update error: %v", err)
			return nil, xerr.NewErrCode(xerr.DbError)
		}
	}

	// 检查用户是否被禁用
	if user.IsDisabled == 1 {
		return nil, xerr.NewErrMsg("账户已被禁用")
	}

	// 4. 生成JWT token
	token, err := l.svcCtx.Jwt.GenerateToken(map[string]interface{}{
		"uid":       user.Id,
		"telephone": user.Telephone,
		"time":      t.Unix(),
	})
	if err != nil {
		l.Logger.Error("WeChatLogin GenerateToken error: %v", err)
		return nil, xerr.NewErrMsg("生成token失败")
	}

	// 5. 保存token到数据库
	expireTime := t.Add(l.svcCtx.Config.Auth.AccessExpire).Unix()

	// 删除旧token
	_ = l.svcCtx.UserTokenModel.DeleteByUid(l.ctx, user.Id)

	// 插入新token
	userToken := &model.UserToken{
		Uid:        user.Id,
		Token:      token,
		ExpireTime: expireTime,
		IsInvalid:  0, // 0=有效
	}

	_, err = l.svcCtx.UserTokenModel.Insert(l.ctx, userToken)
	if err != nil {
		l.Logger.Error("WeChatLogin UserTokenModel.Insert error: %v", err)
		// 这里不返回错误，因为主要功能已经完成
	}

	// 6. 构造返回数据
	userInfo := types.UserInfo{
		Id:             user.Id,
		AppletOpenid:   user.AppletOpenid,
		Telephone:      user.Telephone,
		Nickname:       user.Nickname,
		AvatarImage:    user.AvatarImage,
		Sex:            user.Sex,
		Birthday:       user.Birthday.String,
		IsDisabled:     user.IsDisabled,
		Balance:        user.Balance,
		Level:          user.Level,
		LastLoginTime:  common.TimeToString(common.UnixToTime(user.LastLoginTime)),
		CreatedTime:    common.TimeToString(user.CreatedTime),
		UpdateTime:     common.TimeToString(user.UpdateTime),
		TotalBalance:   user.TotalBalance,
		LevelStartTime: common.TimeToString(common.UnixToTime(user.LevelStartTime)),
		LevelEndTime:   common.TimeToString(common.UnixToTime(user.LevelEndTime)),
		Type:           user.Type,
	}

	return &types.WeChatLoginResp{
		UID:      user.Id,
		Token:    token,
		OpenID:   openidRes.Openid,
		UnionID:  openidRes.Unionid,
		Name:     user.Nickname,
		Avatar:   user.AvatarImage,
		UserInfo: userInfo,
	}, nil
}

// 获取微信access_token的辅助方法
func (l *WeChatLoginLogic) getAccessToken() string {
	// 这里应该实现获取微信access_token的逻辑
	// 可以从缓存中获取，如果过期则重新获取
	// 为了简化，这里返回空字符串，实际项目中需要实现
	return ""
}
