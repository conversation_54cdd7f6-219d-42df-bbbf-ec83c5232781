package user

import (
	"context"
	"engine/api/internal/svc"
	"engine/api/internal/types"
	"engine/common"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserInfoLogic {
	return &GetUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserInfoLogic) GetUserInfo() (resp *types.GetUserInfoResp, err error) {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return nil, xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 查询用户信息
	user, err := l.svcCtx.UserModel.FindOne(l.ctx, uid)
	if err != nil {
		if err == model.ErrNotFound {
			return nil, xerr.NewErrCode(xerr.DataNoExistError)
		}
		l.Logger.Error("GetUserInfo UserModel.FindOne error: %v", err)
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 检查用户是否被禁用
	if user.IsDisabled == 1 {
		return nil, xerr.NewErrMsg("账户已被禁用")
	}

	// 转换数据
	userInfo := types.UserInfo{
		Id:             user.Id,
		AppletOpenid:   user.AppletOpenid,
		Telephone:      user.Telephone,
		Nickname:       user.Nickname,
		AvatarImage:    user.AvatarImage, // vh_user.avatar_image 从微信拿的，存的全路径图片，不需要拼接ALIURL
		Sex:            user.Sex,
		Birthday:       user.Birthday.String,
		IsDisabled:     user.IsDisabled,
		Balance:        user.Balance,
		Level:          user.Level,
		LastLoginTime:  common.TimeToString(common.UnixToTime(user.LastLoginTime)),
		CreatedTime:    common.TimeToString(user.CreatedTime),
		UpdateTime:     common.TimeToString(user.UpdateTime),
		TotalBalance:   user.TotalBalance,
		LevelStartTime: common.TimeToString(common.UnixToTime(user.LevelStartTime)),
		LevelEndTime:   common.TimeToString(common.UnixToTime(user.LevelEndTime)),
		Type:           user.Type,
	}

	return &types.GetUserInfoResp{
		UserInfo: userInfo,
	}, nil
}
