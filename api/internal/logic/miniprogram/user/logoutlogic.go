package user

import (
	"context"
	"engine/api/internal/svc"
	"engine/common/model"
	"engine/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type LogoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLogoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LogoutLogic {
	return &LogoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LogoutLogic) Logout() error {
	// 获取当前用户ID
	uid := l.svcCtx.Jwt.GetUid(l.ctx)
	if uid == 0 {
		return xerr.NewErrCode(xerr.TokenExpireError)
	}

	// 删除用户token
	err := l.svcCtx.UserTokenModel.DeleteByUid(l.ctx, uid)
	if err != nil && err != model.ErrNotFound {
		l.Logger.Error("Logout UserTokenModel.DeleteByUid error: %v", err)
		return xerr.NewErrCode(xerr.DbError)
	}

	return nil
}
