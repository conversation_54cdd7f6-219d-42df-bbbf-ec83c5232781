package middleware

import (
	"context"
	"engine/common/jwtutil"
	"engine/common/model"
	"net/http"
)

type AuthMiddleware struct {
	Jwt            *jwtutil.JWTUtil
	UserTokenModel model.UserTokenModel
}

func NewAuthMiddleware(jwt *jwtutil.JWTUtil, userTokenModel model.UserTokenModel) *AuthMiddleware {
	return &AuthMiddleware{
		Jwt:            jwt,
		UserTokenModel: userTokenModel,
	}
}

func (m *AuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		tokenString := r.Header.Get("Authorization")
		if tokenString == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// 验证JWT token签名和过期时间
		claims, err := m.Jwt.VerifyToken(tokenString)
		if err != nil {
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return
		}

		// 验证token是否在数据库中存在且有效（防止已退出登录的token仍然有效）
		userToken, err := m.UserTokenModel.FindOneByToken(r.Context(), tokenString)
		if err != nil {
			if err == model.ErrNotFound {
				http.Error(w, "Token已失效", http.StatusUnauthorized)
				return
			}
			http.Error(w, "Token验证失败", http.StatusUnauthorized)
			return
		}

		// 检查token是否被标记为失效
		if userToken.IsInvalid == 1 {
			http.Error(w, "Token已失效", http.StatusUnauthorized)
			return
		}

		r = r.WithContext(context.WithValue(r.Context(), "jwt_user", claims))
		next(w, r)
	}
}
