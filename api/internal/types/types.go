// Code generated by goctl. DO NOT EDIT.
package types

type ActiveLotteryActivityParticipantReq struct {
	IdJ
}

type ActiveLotteryActivityResp struct {
	LotteryActivityInfo
}

type AddressCreateReq struct {
	ProvinceId     int64  `json:"province_id" validate:"required" v:"省份ID"`
	CityId         int64  `json:"city_id" validate:"required" v:"城市ID"`
	TownId         int64  `json:"town_id" validate:"required" v:"区县ID"`
	Address        string `json:"address" validate:"required" v:"详细地址"`
	Consignee      string `json:"consignee" validate:"required" v:"收货人"`
	ConsigneePhone string `json:"consignee_phone" validate:"required" v:"收货人电话"`
	ProvinceName   string `json:"province_name" validate:"required" v:"省份名称"`
	CityName       string `json:"city_name" validate:"required" v:"城市名称"`
	TownName       string `json:"town_name" validate:"required" v:"区县名称"`
	Label          string `json:"label,optional" v:"标签"`
	Code           string `json:"code,optional" v:"邮编"`
	IsDefault      int64  `json:"is_default,default=0" validate:"oneof=0 1" v:"是否默认"`
}

type AddressDeleteReq struct {
	Id int64 `json:"id" validate:"required" v:"地址ID"`
}

type AddressDetailReq struct {
	Id int64 `form:"id" validate:"required" v:"地址ID"`
}

type AddressListReq struct {
	Page  int64 `form:"page,default=1"`
	Limit int64 `form:"limit,default=10"`
}

type AddressListResp struct {
	List  []UserAddressInfo `json:"list"`
	Total int64             `json:"total"`
}

type AddressUpdateReq struct {
	Id             int64  `json:"id" validate:"required" v:"地址ID"`
	ProvinceId     int64  `json:"province_id" validate:"required" v:"省份ID"`
	CityId         int64  `json:"city_id" validate:"required" v:"城市ID"`
	TownId         int64  `json:"town_id" validate:"required" v:"区县ID"`
	Address        string `json:"address" validate:"required" v:"详细地址"`
	Consignee      string `json:"consignee" validate:"required" v:"收货人"`
	ConsigneePhone string `json:"consignee_phone" validate:"required" v:"收货人电话"`
	ProvinceName   string `json:"province_name" validate:"required" v:"省份名称"`
	CityName       string `json:"city_name" validate:"required" v:"城市名称"`
	TownName       string `json:"town_name" validate:"required" v:"区县名称"`
	Label          string `json:"label,optional" v:"标签"`
	Code           string `json:"code,optional" v:"邮编"`
	IsDefault      int64  `json:"is_default,default=0" validate:"oneof=0 1" v:"是否默认"`
}

type AdminBannerListReq struct {
	Paging
	Status int64 `form:"status,optional"` // 状态筛选，可选
}

type AdminBannerListResp struct {
	List  []BannerInfo `json:"list"`
	Total int64        `json:"total"`
}

type AdminLabelListReq struct {
	Paging
	Status int64 `form:"status,optional"` // 状态筛选，可选
}

type AdminLabelListResp struct {
	List  []LabelInfo `json:"list"`
	Total int64       `json:"total"`
}

type AdminLotteryActivityListReq struct {
	Paging
	Title  string `form:"title,optional"`  // 活动名称搜索
	Status int64  `form:"status,optional"` // 状态筛选
}

type AdminLotteryActivityListResp struct {
	List  []LotteryActivityInfo `json:"list"`
	Total int64                 `json:"total"`
}

type AdminUserLevelListReq struct {
	Page  int64  `form:"page,default=1"`
	Limit int64  `form:"limit,default=10"`
	Name  string `form:"name,optional"`
	Level int64  `form:"level,optional"`
}

type AdminUserLevelListResp struct {
	List  []UserLevelInfo `json:"list"`
	Total int64           `json:"total"`
}

type AdminUserListReq struct {
	Page       int64  `form:"page,default=1"`
	Limit      int64  `form:"limit,default=10"`
	Telephone  string `form:"telephone,optional"`
	Nickname   string `form:"nickname,optional"`
	Level      int64  `form:"level,optional"`
	Type       int64  `form:"type,optional"`
	IsDisabled int64  `form:"is_disabled,optional"`
}

type AdminUserListResp struct {
	List  []UserInfo `json:"list"`
	Total int64      `json:"total"`
}

type BannerCreateReq struct {
	Picture    string `json:"picture" validate:"required" v:"图片"`
	JumpType   int64  `json:"jump_type" validate:"required,min=1,max=2" v:"跳转方式"`
	IssuedTime string `json:"issued_time" validate:"required" v:"上架时间"`
	SoldTime   string `json:"sold_time" validate:"required" v:"下架时间"`
	Sort       int64  `json:"sort,default=1" validate:"min=1" v:"排序"`
	Status     int64  `json:"status,default=1" validate:"min=0,max=1" v:"状态"`
	JumpValue  string `json:"jump_value,default=1" v:"跳转值"`
}

type BannerDeleteReq struct {
	IdJ
}

type BannerDetailReq struct {
	IdF
}

type BannerInfo struct {
	Id          int64  `json:"id"`
	Picture     string `json:"picture"`
	JumpType    int64  `json:"jump_type"`
	IssuedTime  string `json:"issued_time"`
	SoldTime    string `json:"sold_time"`
	Sort        int64  `json:"sort"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdateTime  string `json:"update_time"`
	VhUid       int64  `json:"vh_uid"`
	VhVosName   string `json:"vh_vos_name"`
	JumpValue   string `json:"jump_value"`
}

type BannerUpdateReq struct {
	IdJ
	Picture    string `json:"picture" validate:"required" v:"图片"`
	JumpType   int64  `json:"jump_type" validate:"required,min=1,max=2" v:"跳转方式"`
	IssuedTime string `json:"issued_time" validate:"required" v:"上架时间"`
	SoldTime   string `json:"sold_time" validate:"required" v:"下架时间"`
	Sort       int64  `json:"sort" validate:"min=1" v:"排序"`
	Status     int64  `json:"status" validate:"min=0,max=1" v:"状态"`
	JumpValue  string `json:"jump_value" v:"跳转值"`
}

type FileDeleteReq struct {
	IdJ
}

type FileInfo struct {
	Id         int64  `json:"id"`
	Path       string `json:"path"`
	Type       int64  `json:"type"`
	ExtendCode string `json:"extend_code"`
}

type FileListReq struct {
	Type int64 `form:"type,options=[1],default=1"` //1海报图
}

type FileListResp struct {
	List []FileInfo `json:"list"`
}

type FileSaveReq struct {
	Type       int64  `json:"type,options=[1]"` //1关注,2取消关注
	Path       string `json:"path" validate:"required" v:"路径"`
	ExtendCode string `json:"extend_code,optional"`
}

type GetUserInfoResp struct {
	UserInfo UserInfo `json:"user_info"`
}

type IdF struct {
	Id int64 `form:"id" validate:"required" v:"数据id"`
}

type IdJ struct {
	Id int64 `json:"id" validate:"required" v:"数据id"`
}

type Img struct {
	Path     string `json:"path"`
	ByteSize int64  `json:"byte_size"`
}

type ImgReSizeReq struct {
	Imgs        []string `json:"imgs"`
	ResizeWidth int64    `json:"resize_width,default=800"`
}

type ImgReSizeResp struct {
	Imgs []Img `json:"imgs"`
}

type LabelCreateReq struct {
	Name   string `json:"name" validate:"required" v:"分类名称"`
	Sort   int64  `json:"sort,default=0" validate:"min=0" v:"排序"`
	Status int64  `json:"status,default=1" validate:"min=0,max=1" v:"状态"`
}

type LabelDeleteReq struct {
	IdJ
}

type LabelDetailReq struct {
	IdF
}

type LabelInfo struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	Sort        int64  `json:"sort"`
	Status      int64  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdateTime  string `json:"update_time"`
}

type LabelUpdateReq struct {
	IdJ
	Name   string `json:"name" validate:"required" v:"分类名称"`
	Sort   int64  `json:"sort" validate:"min=0" v:"排序"`
	Status int64  `json:"status" validate:"min=0,max=1" v:"状态"`
}

type LotteryActivityCreateReq struct {
	Title       string `json:"title" validate:"required" v:"活动名称"`
	Describe    string `json:"describe" validate:"required" v:"活动描述"`
	Status      int64  `json:"status,default=0" validate:"min=0,max=1" v:"活动状态"`
	StartTime   string `json:"start_time" validate:"required" v:"开始时间"`
	GoodsId     int64  `json:"goods_id" validate:"required" v:"商品ID"`
	GoodsTitle  string `json:"goods_title" validate:"required" v:"商品标题"`
	GoodsImg    string `json:"goods_img" validate:"required" v:"商品图片"`
	Total       int64  `json:"total" validate:"required,min=1" v:"开奖人数门槛"`
	WinnerCount int64  `json:"winner_count" validate:"required,min=1" v:"中奖人数配额"`
}

type LotteryActivityInfo struct {
	Id               int64  `json:"id"`
	Title            string `json:"title"`
	Describe         string `json:"describe"`
	Status           int64  `json:"status"`
	StartTime        string `json:"start_time"`
	GoodsId          int64  `json:"goods_id"`
	GoodsTitle       string `json:"goods_title"`
	GoodsImg         string `json:"goods_img"`
	Total            int64  `json:"total"`
	WinnerCount      int64  `json:"winner_count"`
	ParticipantCount int64  `json:"participant_count"`
	CreateTime       string `json:"create_time"`
}

type LotteryActivityStatusUpdateReq struct {
	IdJ
	Status int64 `json:"status" validate:"min=0,max=1" v:"活动状态"`
}

type LotteryActivityUpdateReq struct {
	IdJ
	Title       string `json:"title" validate:"required" v:"活动名称"`
	Describe    string `json:"describe" validate:"required" v:"活动描述"`
	StartTime   string `json:"start_time" validate:"required" v:"开始时间"`
	GoodsId     int64  `json:"goods_id" validate:"required" v:"商品ID"`
	GoodsTitle  string `json:"goods_title" validate:"required" v:"商品标题"`
	GoodsImg    string `json:"goods_img" validate:"required" v:"商品图片"`
	Total       int64  `json:"total" validate:"required,min=1" v:"开奖人数门槛"`
	WinnerCount int64  `json:"winner_count" validate:"required,min=1" v:"中奖人数配额"`
}

type MiniPayResp struct {
	AppId     string `json:"app_id"`
	TimeStamp string `json:"time_stamp"`
	NonceStr  string `json:"nonce_str"`
	Package   string `json:"package"`
	SignType  string `json:"sign_type"`
	PaySign   string `json:"pay_sign"`
}

type MiniProgramBannerInfo struct {
	Id        int64  `json:"id"`
	Picture   string `json:"picture"`
	JumpType  int64  `json:"jump_type"`
	JumpValue string `json:"jump_value"`
}

type MiniProgramBannerListResp struct {
	List []MiniProgramBannerInfo `json:"list"`
}

type Paging struct {
	Page  int64 `form:"page,default=1"`
	Limit int64 `form:"limit,default=10"`
}

type UserAddressInfo struct {
	Id             int64  `json:"id"`
	Uid            int64  `json:"uid"`
	ProvinceId     int64  `json:"province_id"`
	CityId         int64  `json:"city_id"`
	TownId         int64  `json:"town_id"`
	Address        string `json:"address"`
	IsDefault      int64  `json:"is_default"`
	Label          string `json:"label"`
	Code           string `json:"code"`
	Consignee      string `json:"consignee"`
	ConsigneePhone string `json:"consignee_phone"`
	ProvinceName   string `json:"province_name"`
	CityName       string `json:"city_name"`
	TownName       string `json:"town_name"`
	CreatedTime    string `json:"created_time"`
	UpdateTime     string `json:"update_time"`
}

type UserInfo struct {
	Id             int64   `json:"id"`
	AppletOpenid   string  `json:"applet_openid"`
	Telephone      string  `json:"telephone"`
	Nickname       string  `json:"nickname"`
	AvatarImage    string  `json:"avatar_image"`
	Sex            int64   `json:"sex"`
	Birthday       string  `json:"birthday"`
	IsDisabled     int64   `json:"is_disabled"`
	Balance        float64 `json:"balance"`
	Level          int64   `json:"level"`
	LastLoginTime  string  `json:"last_login_time"`
	CreatedTime    string  `json:"created_time"`
	UpdateTime     string  `json:"update_time"`
	TotalBalance   float64 `json:"total_balance"`
	LevelStartTime string  `json:"level_start_time"`
	LevelEndTime   string  `json:"level_end_time"`
	Type           int64   `json:"type"`
}

type UserLevelCreateReq struct {
	Level            int64                    `json:"level" validate:"required" v:"等级"`
	Name             string                   `json:"name" validate:"required" v:"等级名称"`
	LevelName        string                   `json:"level_name" validate:"required" v:"展示名称"`
	ShareBrokerage   float64                  `json:"share_brokerage,default=0" v:"分享返佣比例"`
	PaymentBrokerage float64                  `json:"payment_brokerage,default=0" v:"购买返佣比例"`
	CashBrokerage    float64                  `json:"cash_brokerage,default=0" v:"提现佣金比例"`
	CashMinAmount    float64                  `json:"cash_min_amount,default=0" v:"提现最低金额"`
	Duration         int64                    `json:"duration,default=0" v:"持续时间(天)"`
	Tasks            []UserLevelTaskCreateReq `json:"tasks,optional"`
}

type UserLevelDeleteReq struct {
	Id int64 `json:"id" validate:"required" v:"等级ID"`
}

type UserLevelDetailReq struct {
	Id int64 `form:"id" validate:"required" v:"等级ID"`
}

type UserLevelInfo struct {
	Id               int64               `json:"id"`
	Level            int64               `json:"level"`
	Name             string              `json:"name"`
	LevelName        string              `json:"level_name"`
	ShareBrokerage   float64             `json:"share_brokerage"`
	PaymentBrokerage float64             `json:"payment_brokerage"`
	CashBrokerage    float64             `json:"cash_brokerage"`
	CashMinAmount    float64             `json:"cash_min_amount"`
	Duration         int64               `json:"duration"`
	CreatedTime      string              `json:"created_time"`
	UpdateTime       string              `json:"update_time"`
	Tasks            []UserLevelTaskInfo `json:"tasks"`
}

type UserLevelTaskCreateReq struct {
	Name string `json:"name" validate:"required" v:"任务名称"`
	Type int64  `json:"type" validate:"required,oneof=1 2" v:"任务类型:1=分享数,2=考试"`
	Num  int64  `json:"num" validate:"required" v:"完成数量"`
}

type UserLevelTaskInfo struct {
	Id    int64  `json:"id"`
	Level int64  `json:"level"`
	Name  string `json:"name"`
	Type  int64  `json:"type"`
	Num   int64  `json:"num"`
}

type UserLevelTaskUpdateReq struct {
	Id   int64  `json:"id,optional" v:"任务ID"`
	Name string `json:"name" validate:"required" v:"任务名称"`
	Type int64  `json:"type" validate:"required,oneof=1 2" v:"任务类型:1=分享数,2=考试"`
	Num  int64  `json:"num" validate:"required" v:"完成数量"`
}

type UserLevelUpdateReq struct {
	Id               int64                    `json:"id" validate:"required" v:"等级ID"`
	Level            int64                    `json:"level" validate:"required" v:"等级"`
	Name             string                   `json:"name" validate:"required" v:"等级名称"`
	LevelName        string                   `json:"level_name" validate:"required" v:"展示名称"`
	ShareBrokerage   float64                  `json:"share_brokerage,default=0" v:"分享返佣比例"`
	PaymentBrokerage float64                  `json:"payment_brokerage,default=0" v:"购买返佣比例"`
	CashBrokerage    float64                  `json:"cash_brokerage,default=0" v:"提现佣金比例"`
	CashMinAmount    float64                  `json:"cash_min_amount,default=0" v:"提现最低金额"`
	Duration         int64                    `json:"duration,default=0" v:"持续时间(天)"`
	Tasks            []UserLevelTaskUpdateReq `json:"tasks,optional"`
}

type UserSetDisabledReq struct {
	Id         int64 `json:"id" validate:"required" v:"用户ID"`
	IsDisabled int64 `json:"is_disabled" validate:"min=0,max=1" v:"禁用状态"`
}

type WeChatAppletOneclickLoginReq struct {
	Code      string `json:"code" validate:"required" v:"临时授权码"`
	PCode     string `json:"p_code" validate:"required" v:"手机号获取凭证"`
	SourceUid string `json:"source_uid,optional" validate:"omitempty" v:"来源用户ID"`
}

type WeChatAppletOneclickLoginResp struct {
	UID     int64  `json:"uid"`
	Token   string `json:"token"`
	OpenID  string `json:"openid"`
	UnionID string `json:"unionid"`
	Name    string `json:"name"`
	Avatar  string `json:"avatar"`
}

type WeChatLoginReq struct {
	Code      string `json:"code" validate:"required" v:"临时授权码"`
	PCode     string `json:"p_code" validate:"required" v:"手机号获取凭证"`
	SourceUid string `json:"source_uid,optional" validate:"omitempty" v:"来源用户ID"`
}

type WeChatLoginResp struct {
	UID      int64    `json:"uid"`
	Token    string   `json:"token"`
	OpenID   string   `json:"openid"`
	UnionID  string   `json:"unionid"`
	Name     string   `json:"name"`
	Avatar   string   `json:"avatar"`
	UserInfo UserInfo `json:"user_info"`
}
